#include "pid_app.h"

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环



// 增量式PID：P-稳定性，I-响应性，D-准确性


PidParams_t pid_params_left = {
    .kp = 0.60f,
    .ki = 0.068f,
    .kd = 4.665f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
		.kp = 1.5f,
    .ki = 0.055f,
    .kd = 6.5f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};


void PID_Init(void)
{
  pid_init(&pid_speed_left,
           pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
           0.0f, pid_params_left.out_max);
  
  pid_init(&pid_speed_right,
           pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
           0.0f, pid_params_right.out_max);

  
  pid_set_target(&pid_speed_left, 30);
  pid_set_target(&pid_speed_right, 40);
}


uint8_t pid_running = 1; // PID 控制使能开关


void pid_task(void)
{
	if(pid_running == 0) return;

	float output_left, output_right;

	
	// 使用增量式 PID 计算利用速度环计算输出
	output_left = pid_calculate_incremental(&pid_speed_left, left_encoder.speed_cm_s);
	output_right = pid_calculate_incremental(&pid_speed_right, right_encoder.speed_cm_s);
    
	
	// 设置电机速度
	Motor_SetSpeed(&left_motor, output_left);
	Motor_SetSpeed(&right_motor, output_right);
	
	
//	my_printf(&huart1,"{left}%d,%d\r\n",(int)(pid_speed_left.target*100),(int)(left_encoder.speed_cm_s*100));
//	my_printf(&huart1,"{right}%d,%d\r\n",(int)(pid_speed_right.target*100),(int)(right_encoder.speed_cm_s*100));	
//	my_printf(&huart1,"{left}%.2f,%.2f\r\n",pid_speed_left.target,left_encoder.speed_cm_s);
//	my_printf(&huart1,"{right}%.2f,%.2f\r\n",pid_speed_right.target,right_encoder.speed_cm_s);
}

