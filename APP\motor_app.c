#include "motor_app.h"
#include <math.h>
#include <stdint.h>

Motor_t left_motor;
Motor_t right_motor;

void Motor_Init(void)
{
	TB6612_Init(1);
	//假设电机1正装 电机2为反装
	Motor_Create(&left_motor,&htim2,TIM_CHANNEL_1,GPIOA,GPIO_PIN_2,GPIOA,GPIO_PIN_3,0);
	Motor_Create(&right_motor,&htim2,TIM_CHANNEL_2,GPIOB,GPIO_PIN_12,GPIOB,GPIO_PIN_13,1);
	Motor_SetSpeed(&left_motor, 400);
	Motor_SetSpeed(&right_motor, 400);
}



