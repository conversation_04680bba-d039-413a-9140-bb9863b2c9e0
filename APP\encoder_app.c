#include "encoder_app.h"

// 左右电机编码器
Encoder left_encoder;
Encoder right_encoder;

/**
 * @brief 初始化编码器应用
 */
void Encoder_Init(void)
{
	// 左编码器方向，使其与右编码器方向一致
	Encoder_Driver_Init(&left_encoder, &htim3, 1);  // 左编码器正转
	Encoder_Driver_Init(&right_encoder, &htim4, 0); // 保持右编码器设置
}

/**
 * @brief 编码器应用运行任务 (应由调度器周期性调用)
 * 标准版本 - 显示稳定的速度值
 */
void encoder_task(void)
{
	static uint32_t debug_counter = 0;
	debug_counter++;
	
	
	Encoder_Driver_Update(&left_encoder);
	Encoder_Driver_Update(&right_encoder);
	
//	// 每50次调用(0.5秒)显示一次详细信息
//	if (debug_counter % 50 == 0)
//	{
//		my_printf(&huart1,"\r\n=== Encoder Debug Info ===\r\n");
//		my_printf(&huart1,"PPR: %d, Wheel: %.1fcm, Circumference: %.2fcm\r\n",
//			ENCODER_PPR, WHEEL_DIAMETER_CM, WHEEL_CIRCUMFERENCE_CM);
//		my_printf(&huart1,"Left  - Count:%2d, Total:%4ld\r\n",
//			left_encoder.count, left_encoder.total_count);
//		my_printf(&huart1,"Right - Count:%2d, Total:%4ld\r\n",
//			right_encoder.count, right_encoder.total_count);
//		my_printf(&huart1,"==========================\r\n\r\n");
//	}

}
