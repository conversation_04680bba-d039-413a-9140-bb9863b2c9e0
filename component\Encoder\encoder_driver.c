#include "encoder_driver.h"

/**
 * @brief 初始化编码器驱动
 */
void Encoder_Driver_Init(Encoder* encoder, TIM_HandleTypeDef *htim, unsigned char reverse)
{
	encoder->htim = htim;
	encoder->reverse = reverse;

	// 启动定时器的编码器模式
	HAL_TIM_Encoder_Start(encoder->htim, TIM_CHANNEL_ALL);

	// 清零计数器
	__HAL_TIM_SetCounter(encoder->htim, 0);

	// 初始化数据结构
	encoder->count = 0;
	encoder->total_count = 0;
	encoder->speed_cm_s = 0.0f;
	encoder->speed_filtered = 0.0f;
	encoder->last_update_time = HAL_GetTick();
}

/**
 * @brief 更新编码器数据（根据调用频率来确定采样间隔时间）
 */
void Encoder_Driver_Update(Encoder* encoder)
{
	
  // 1. 读取原始计数值
  encoder->count = (int16_t)__HAL_TIM_GetCounter(encoder->htim);
  
  // 2. 处理编码器反向
  encoder->count = encoder->reverse == 0 ? encoder->count : -encoder->count;

  // 3. 清零硬件计数器，为下个周期做准备
  __HAL_TIM_SetCounter(encoder->htim, 0);

  // 4. 累计总数
  encoder->total_count += encoder->count;

//  // 5. 计算速度 (cm/s)
//  // 速度 = (计数值 / PPR) * 周长 / 采样时间
//  encoder->speed_cm_s = (float)encoder->count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / SAMPLING_TIME_S;
	
	// 5. 获取当前时间
	uint32_t current_time = HAL_GetTick();
	float actual_dt = (current_time - encoder->last_update_time) / 1000.0f; // 转换为秒

	// 6. 计算速度 (cm/s)
	// 使用实际时间间隔而不是固定的采样时间
	if (actual_dt > 0.001f) // 避免除零，最小1ms间隔
	{
		float instant_speed = 0.0f;

		// 只有当计数值超过阈值时才计算速度，减少噪声影响
		if (abs(encoder->count) >= MIN_COUNT_THRESHOLD)
		{
			instant_speed = (float)encoder->count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / actual_dt;
		}
		// 如果计数值很小，让速度逐渐衰减到0
		else if (abs(encoder->count) == 1)
		{
			instant_speed = (float)encoder->count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / actual_dt * 0.5f;
		}

		// 7. 应用低通滤波平滑速度
		encoder->speed_cm_s = SPEED_FILTER_ALPHA * instant_speed + (1.0f - SPEED_FILTER_ALPHA) * encoder->speed_cm_s;

		// 8. 更新滤波后的速度（用于更平滑的显示）
		encoder->speed_filtered = 0.15f * encoder->speed_cm_s + 0.85f * encoder->speed_filtered;
	}

	// 9. 更新时间戳
	encoder->last_update_time = current_time;
}


