--cpu Cortex-M3
"pwm_demo_01\startup_stm32f103xb.o"
"pwm_demo_01\main.o"
"pwm_demo_01\gpio.o"
"pwm_demo_01\dma.o"
"pwm_demo_01\tim.o"
"pwm_demo_01\usart.o"
"pwm_demo_01\stm32f1xx_it.o"
"pwm_demo_01\stm32f1xx_hal_msp.o"
"pwm_demo_01\stm32f1xx_hal_gpio_ex.o"
"pwm_demo_01\stm32f1xx_hal_tim.o"
"pwm_demo_01\stm32f1xx_hal_tim_ex.o"
"pwm_demo_01\stm32f1xx_hal.o"
"pwm_demo_01\stm32f1xx_hal_rcc.o"
"pwm_demo_01\stm32f1xx_hal_rcc_ex.o"
"pwm_demo_01\stm32f1xx_hal_gpio.o"
"pwm_demo_01\stm32f1xx_hal_dma.o"
"pwm_demo_01\stm32f1xx_hal_cortex.o"
"pwm_demo_01\stm32f1xx_hal_pwr.o"
"pwm_demo_01\stm32f1xx_hal_flash.o"
"pwm_demo_01\stm32f1xx_hal_flash_ex.o"
"pwm_demo_01\stm32f1xx_hal_exti.o"
"pwm_demo_01\stm32f1xx_hal_uart.o"
"pwm_demo_01\system_stm32f1xx.o"
"pwm_demo_01\motor_driver.o"
"pwm_demo_01\ringbuffer.o"
"pwm_demo_01\encoder_driver.o"
"pwm_demo_01\pid.o"
"pwm_demo_01\scheduler.o"
"pwm_demo_01\motor_app.o"
"pwm_demo_01\usart_app.o"
"pwm_demo_01\encoder_app.o"
"pwm_demo_01\pid_app.o"
--library_type=microlib --strict --scatter "PWM_demo_01\PWM_demo_01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "PWM_demo_01.map" -o PWM_demo_01\PWM_demo_01.axf