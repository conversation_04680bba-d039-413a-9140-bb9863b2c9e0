#ifndef MYDEFINE_H
#define MYDEFINE_H

#include "main.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

#include "stdio.h"
#include "stdlib.h"
#include "stdarg.h"
#include "string.h"

#include "ringbuffer.h"
#include "pid.h"

#include "motor_driver.h"
#include "encoder_driver.h"

#include "scheduler.h"
#include "motor_app.h"
#include "encoder_app.h"
#include "pid_app.h"
#include "usart_app.h"

extern TIM_HandleTypeDef htim2;
extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern uint8_t uart_rx_dma_buffer[128];
extern struct rt_ringbuffer uart_ringbuffer;
extern uint8_t ringbuffer_pool[128];

extern Motor_t right_motor;
extern Motor_t left_motor; 

#endif



