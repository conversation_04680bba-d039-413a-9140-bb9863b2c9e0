#ifndef __ENCODER_DRIVER_H__
#define __ENCODER_DRIVER_H__

#include "mydefine.h"

// 编码器每转一圈的脉冲数 (PPR)
// 请根据实际编码器规格调整这个值
#define ENCODER_PPR (13*30*4) // 13线/相, 30倍减速比, 4倍频 = 1560 PPR

// 车轮直径 (单位: 厘米)
#define WHEEL_DIAMETER_CM 6.5f

// 自动计算周长和采样时间
#define PI 3.14159265f
#define WHEEL_CIRCUMFERENCE_CM (WHEEL_DIAMETER_CM * PI)
#define SAMPLING_TIME_S 0.01f // 采样时间, 与 Scheduler 中的任务周期一致 (10ms)

// 速度滤波参数 - 用于平滑速度计算
#define SPEED_FILTER_ALPHA 0.2f // 低通滤波系数 (0-1, 越小越平滑)
#define MIN_COUNT_THRESHOLD 2    // 最小计数阈值，小于此值认为是噪声

/**
 * @brief 编码器数据结构体
 */
typedef struct
{
	TIM_HandleTypeDef *htim;	// 定时器
	unsigned char reverse;	// 编码器的方向是否反转。0-正常，1-反转
	int16_t count;          	// 当前采样周期内的原始计数值
	int32_t total_count;    	// 累计总计数值
	float speed_cm_s;    		// 计算出的速度 (cm/s)
	float speed_filtered;     // 滤波后的速度 (cm/s)
	uint32_t last_update_time; // 上次更新时间 (ms)
} Encoder;

void Encoder_Driver_Init(Encoder* encoder, TIM_HandleTypeDef *htim, unsigned char reverse);
void Encoder_Driver_Update(Encoder* encoder);

extern Encoder left_encoder;
extern Encoder right_encoder;

#endif
